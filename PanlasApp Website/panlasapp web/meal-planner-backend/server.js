require('dotenv').config({ path: __dirname + '/.env' });
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require('path');
const { trackAPICall } = require('./middleware/analytics');
const userRoutes = require('./routes/userRoutes');
const mealRoutes = require('./routes/mealRoutes');
const mealPlanRoutes = require('./routes/mealPlanRoutes');
const adminRoutes = require('./routes/adminRoutes');
const activityRoutes = require('./routes/activityRoutes');
const feedbackRoutes = require('./routes/feedbackRoutes');
const passwordResetRoutes = require('./routes/passwordReset');
const analyticsRoutes = require('./routes/analyticsRoutes');
const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// Trust proxy for accurate IP addresses
app.set('trust proxy', true);

// Analytics middleware for API tracking
app.use('/api', trackAPICall);

// Serve static files for food images
app.use('/imagesfood', express.static(path.join(__dirname, '../public/imagesfood')));
console.log('Static files served from:', path.join(__dirname, '../public/imagesfood'));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Request body:', req.body);
    console.log('Headers:', req.headers);
  next();
});

// Routes
console.log('Setting up API routes');
app.use('/api/users', userRoutes);
app.use('/api/meals', mealRoutes);
// Change this line to match your frontend API calls
app.use('/api/meal-plans', mealPlanRoutes); // Changed from '/api/mealplans' to '/api/meal-plans'
console.log('Loading admin routes...');
app.use('/api/admin', adminRoutes);
console.log('Admin routes loaded');
app.use('/api/activity', activityRoutes);
app.use('/api/feedback', feedbackRoutes);
app.use('/api/password-reset', passwordResetRoutes);
app.use('/api/analytics', analyticsRoutes);
console.log('Feedback routes added');
console.log('Password reset routes added');

// Health check route
app.get('/', (req, res) => {
  res.send('Filipino Meal Planner API is running');
});

// Replace the debug route setup code with this simpler version
// Debug route setup - only in development
if (process.env.NODE_ENV !== 'production') {
  // Add a special route that will list all routes
  app.get('/api/routes', (req, res) => {
    const routes = [];

    // User routes
    app._router.stack.forEach(middleware => {
      if (middleware.route) {
        // Routes registered directly on the app
        routes.push({
          path: middleware.route.path,
          methods: Object.keys(middleware.route.methods).map(method => method.toUpperCase())
        });
      } else if (middleware.name === 'router') {
        // Router middleware
        middleware.handle.stack.forEach(handler => {
          if (handler.route) {
            let path = '';

            // Extract the base path
            if (middleware.regexp) {
              const match = middleware.regexp.toString().match(/\/\^(.*?)\\\//);
              if (match && match[1]) {
                path = '/' + match[1].replace(/\\\//g, '/');
              }
            }

            routes.push({
              path: path + handler.route.path,
              methods: Object.keys(handler.route.methods).map(method => method.toUpperCase())
            });
          }
        });
      }
    });

    res.json(routes);
  });

  console.log('\nAPI Routes debug endpoint available at: /api/routes');
  console.log('');
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler caught:', err);
  res.status(500).json({ message: 'Server error', error: err.message });
});

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/meal_planner';
console.log('Attempting to connect to MongoDB at:', MONGODB_URI);
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('MongoDB connected successfully');

    // Print connection details
    console.log('Connection details:');
    console.log('- Database name:', mongoose.connection.name);
    console.log('- Host:', mongoose.connection.host);
    console.log('- Port:', mongoose.connection.port);

    // List all collections
    mongoose.connection.db.listCollections().toArray()
      .then(collections => {
        console.log('Collections in database:');
        collections.forEach(collection => {
          console.log('- ' + collection.name);
        });
      })
      .catch(err => {
        console.error('Error listing collections:', err);
      });

    // Start server AFTER successful database connection
    const PORT = process.env.PORT || 5000;
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    console.error('Connection details:', {
      uri: MONGODB_URI,
      mongooseVersion: mongoose.version
    });
    process.exit(1); // Exit the process if we can't connect to the database
  });

console.log('Server initialization complete');
