import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  ScrollView,
  FlatList,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { userAPI } from '../../services/api';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { useResponsiveScreen, getResponsiveFontSize, getResponsiveSpacing, getResponsiveDimensions } from '../../utils/responsive';

const ProfileScreen = ({ navigation }) => {
  const [familyMembers, setFamilyMembers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const { user, logout, refreshUser } = useAuth();
  const { size: screenSize } = useResponsiveScreen();
  const dimensions = getResponsiveDimensions(screenSize);

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  useEffect(() => {
    loadFamilyMembers();
    refreshUser(); // Refresh user data to get latest DOB
  }, []);

  const loadFamilyMembers = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getFamilyMembers();
      setFamilyMembers(response.data || []);
    } catch (error) {
      console.error('Error loading family members:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      loadFamilyMembers(),
      refreshUser() // Also refresh user data
    ]);
    setRefreshing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
        </View>

        {/* User Profile Section */}
        <View style={styles.userSection}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {user?.firstName?.charAt(0) || 'U'}
              </Text>
            </View>
          </View>
          <Text style={styles.userName}>
            {user?.firstName} {user?.lastName}
          </Text>
          <Text style={styles.userEmail}>{user?.email}</Text>
          {user?.dateOfBirth && (
            <Text style={styles.userAge}>
              Age: {calculateAge(user.dateOfBirth)} years old
            </Text>
          )}
        </View>

        {/* Family Members Section */}
        {familyMembers.length > 0 && (
          <View style={styles.familyGroupSection}>
            <View style={styles.familyGroupHeader}>
              <Text style={styles.familyGroupTitle}>Family Members</Text>
              <TouchableOpacity
                style={styles.manageFamilyButton}
                onPress={() => navigation.navigate('Family')}
              >
                <Text style={styles.manageFamilyText}>Manage</Text>
                <Ionicons name="chevron-forward" size={16} color={colors.primary} />
              </TouchableOpacity>
            </View>

            {/* Family Members Grid */}
            <View style={styles.familyMembersGrid}>
              {familyMembers.slice(0, 5).map((member, index) => (
                <View key={member._id || member.id} style={styles.familyMemberGridCard}>
                  <View style={styles.familyMemberGridAvatar}>
                    <Text style={styles.familyMemberGridAvatarText}>
                      {member.name.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                  <Text style={styles.familyMemberGridName} numberOfLines={1}>
                    {member.name}
                  </Text>
                  {member.dateOfBirth && (
                    <Text style={styles.familyMemberAge}>
                      {calculateAge(member.dateOfBirth)} years
                    </Text>
                  )}
                  {member.dietaryPreferences?.restrictions?.length > 0 && (
                    <View style={styles.restrictionBadge}>
                      <Text style={styles.restrictionBadgeText}>
                        {member.dietaryPreferences.restrictions[0]}
                      </Text>
                    </View>
                  )}
                </View>
              ))}

              {/* Add Member Card */}
              <TouchableOpacity
                style={styles.addMemberCard}
                onPress={() => navigation.navigate('Family')}
              >
                <View style={styles.addMemberIcon}>
                  <Ionicons name="add" size={24} color={colors.primary} />
                </View>
                <Text style={styles.addMemberText}>Add Member</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

      {/* Menu Items */}
      <View style={styles.menuSection}>
        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <Ionicons name="person-outline" size={24} color={colors.primary} />
            <Text style={styles.menuItemText}>Edit Profile</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>



        <TouchableOpacity
          style={styles.menuItem}
          onPress={() => navigation.navigate('Family')}
        >
          <View style={styles.menuItemLeft}>
            <Ionicons name="people-outline" size={24} color={colors.primary} />
            <Text style={styles.menuItemText}>Family Members</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <Ionicons name="help-circle-outline" size={24} color={colors.primary} />
            <Text style={styles.menuItemText}>Help Center</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>

      {/* Logout Button */}
      <View style={styles.logoutSection}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Ionicons name="log-out-outline" size={24} color={colors.secondary} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  userSection: {
    backgroundColor: colors.surface,
    alignItems: 'center',
    paddingVertical: spacing.xl,
    marginBottom: spacing.md,
  },
  avatarContainer: {
    marginBottom: spacing.md,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: fonts.sizes.xxlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  userName: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
  },
  userAge: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
    fontStyle: 'italic',
  },
  familyGroupSection: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
    padding: spacing.md,
  },
  familyGroupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  familyGroupTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },

  menuSection: {
    backgroundColor: colors.surface,
    marginBottom: spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.md,
  },
  logoutSection: {
    backgroundColor: colors.surface,
    marginBottom: spacing.xl,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
  },
  logoutText: {
    fontSize: fonts.sizes.medium,
    color: colors.secondary,
    marginLeft: spacing.md,
    fontWeight: '500',
  },
  manageFamilyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  manageFamilyText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    marginRight: spacing.xs,
    fontWeight: '500',
  },
  familyMembersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  familyMemberGridCard: {
    width: '30%',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  familyMemberGridAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.secondary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  familyMemberGridAvatarText: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
  familyMemberGridName: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  familyMemberAge: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  restrictionBadge: {
    backgroundColor: colors.warning || '#FFA500',
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: borderRadius.small,
  },
  restrictionBadgeText: {
    fontSize: fonts.sizes.tiny || 10,
    color: colors.surface,
    fontWeight: '500',
  },
  addMemberCard: {
    width: '30%',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
  },
  addMemberIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  addMemberText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    textAlign: 'center',
    fontWeight: '500',
  },
  noFamilyContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  noFamilyDescription: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.md,
    paddingHorizontal: spacing.lg,
    lineHeight: 20,
  },
});

export default ProfileScreen;
